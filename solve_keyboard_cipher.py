#!/usr/bin/env python3

def qwerty_to_dvorak(text):
    """Convert text from QWERTY layout to Dvorak layout"""
    # QWERTY keyboard layout
    qwerty = "`1234567890-=qwertyuiop[]\\asdfghjkl;'zxcvbnm,./"
    qwerty += "~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:\"ZXCVBNM<>?"
    
    # Dvorak keyboard layout  
    dvorak = "`1234567890[]',.pyfgcrl/=\\aoeuidhtns-;qjkxbmwvz"
    dvorak += "~!@#$%^&*(){}\"<>PYFGCRL?+|AOEUIDHTNS_:QJKXBMWVZ"
    
    # Create translation table
    translation = str.maketrans(qwerty, dvorak)
    
    return text.translate(translation)

def dvorak_to_qwerty(text):
    """Convert text from Dvorak layout to QWERTY layout"""
    # Dvorak keyboard layout
    dvorak = "`1234567890[]',.pyfgcrl/=\\aoeuidhtns-;qjkxbmwvz"
    dvorak += "~!@#$%^&*(){}\"<>PYFGCRL?+|AOEUIDHTNS_:QJKXBMWVZ"
    
    # QWERTY keyboard layout
    qwerty = "`1234567890-=qwertyuiop[]\\asdfghjkl;'zxcvbnm,./"
    qwerty += "~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:\"ZXCVBNM<>?"
    
    # Create translation table
    translation = str.maketrans(dvorak, qwerty)
    
    return text.translate(translation)

def keyboard_shift(text, shift_amount):
    """Shift each character by a certain amount on the keyboard"""
    # Define keyboard rows
    row1 = "`1234567890-="
    row2 = "qwertyuiop[]\\"
    row3 = "asdfghjkl;'"
    row4 = "zxcvbnm,./"

    # Include uppercase and symbols
    row1_upper = "~!@#$%^&*()_+"
    row2_upper = "QWERTYUIOP{}|"
    row3_upper = "ASDFGHJKL:\""
    row4_upper = "ZXCVBNM<>?"

    result = ""
    for char in text:
        shifted = char

        # Check each row
        for row in [row1, row2, row3, row4, row1_upper, row2_upper, row3_upper, row4_upper]:
            if char in row:
                idx = row.index(char)
                new_idx = (idx + shift_amount) % len(row)
                shifted = row[new_idx]
                break

        result += shifted

    return result

def try_other_layouts(text):
    """Try other common keyboard layout conversions"""

    # QWERTY to AZERTY
    qwerty = "qwertyuiopasdfghjklzxcvbnm"
    azerty = "azertyuiopqsdfghjklmwxcvbn"
    qwerty_to_azerty = str.maketrans(qwerty, azerty)

    # QWERTY to QWERTZ
    qwertz = "qwertzuiopasdfghjklyxcvbnm"
    qwerty_to_qwertz = str.maketrans(qwerty, qwertz)

    print("QWERTY to AZERTY:", text.translate(qwerty_to_azerty))
    print("QWERTY to QWERTZ:", text.translate(qwerty_to_qwertz))

    # Try keyboard shifts
    print("\nKeyboard shifts:")
    for shift in range(-5, 6):
        if shift != 0:
            shifted = keyboard_shift(text, shift)
            print(f"Shift {shift:+d}: {shifted}")

if __name__ == "__main__":
    cipher_text = "LO<F+XYD}6UO2B7)-B+6J2+Qt9M5+J4T6{"
    
    print("Original cipher:", cipher_text)
    print()
    
    print("QWERTY to Dvorak:", qwerty_to_dvorak(cipher_text))
    print("Dvorak to QWERTY:", dvorak_to_qwerty(cipher_text))
    print()
    
    try_other_layouts(cipher_text.lower())
