#!/usr/bin/env python3

def qwerty_to_dvorak(text):
    """Convert text from QWERTY layout to Dvorak layout"""
    # QWERTY keyboard layout
    qwerty = "`1234567890-=qwertyuiop[]\\asdfghjkl;'zxcvbnm,./"
    qwerty += "~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:\"ZXCVBNM<>?"
    
    # Dvorak keyboard layout  
    dvorak = "`1234567890[]',.pyfgcrl/=\\aoeuidhtns-;qjkxbmwvz"
    dvorak += "~!@#$%^&*(){}\"<>PYFGCRL?+|AOEUIDHTNS_:QJKXBMWVZ"
    
    # Create translation table
    translation = str.maketrans(qwerty, dvorak)
    
    return text.translate(translation)

def dvorak_to_qwerty(text):
    """Convert text from Dvorak layout to QWERTY layout"""
    # Dvorak keyboard layout
    dvorak = "`1234567890[]',.pyfgcrl/=\\aoeuidhtns-;qjkxbmwvz"
    dvorak += "~!@#$%^&*(){}\"<>PYFGCRL?+|AOEUIDHTNS_:QJKXBMWVZ"
    
    # QWERTY keyboard layout
    qwerty = "`1234567890-=qwertyuiop[]\\asdfghjkl;'zxcvbnm,./"
    qwerty += "~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:\"ZXCVBNM<>?"
    
    # Create translation table
    translation = str.maketrans(dvorak, qwerty)
    
    return text.translate(translation)

def keyboard_shift(text, shift_amount):
    """Shift each character by a certain amount on the keyboard"""
    # Define keyboard rows
    row1 = "`1234567890-="
    row2 = "qwertyuiop[]\\"
    row3 = "asdfghjkl;'"
    row4 = "zxcvbnm,./"

    # Include uppercase and symbols
    row1_upper = "~!@#$%^&*()_+"
    row2_upper = "QWERTYUIOP{}|"
    row3_upper = "ASDFGHJKL:\""
    row4_upper = "ZXCVBNM<>?"

    result = ""
    for char in text:
        shifted = char

        # Check each row
        for row in [row1, row2, row3, row4, row1_upper, row2_upper, row3_upper, row4_upper]:
            if char in row:
                idx = row.index(char)
                new_idx = (idx + shift_amount) % len(row)
                shifted = row[new_idx]
                break

        result += shifted

    return result

def try_other_layouts(text):
    """Try other common keyboard layout conversions"""

    # QWERTY to AZERTY
    qwerty = "qwertyuiopasdfghjklzxcvbnm"
    azerty = "azertyuiopqsdfghjklmwxcvbn"
    qwerty_to_azerty = str.maketrans(qwerty, azerty)

    # QWERTY to QWERTZ
    qwertz = "qwertzuiopasdfghjklyxcvbnm"
    qwerty_to_qwertz = str.maketrans(qwerty, qwertz)

    print("QWERTY to AZERTY:", text.translate(qwerty_to_azerty))
    print("QWERTY to QWERTZ:", text.translate(qwerty_to_qwertz))

    # Try keyboard shifts
    print("\nKeyboard shifts:")
    for shift in range(-5, 6):
        if shift != 0:
            shifted = keyboard_shift(text, shift)
            print(f"Shift {shift:+d}: {shifted}")

if __name__ == "__main__":
    cipher_text = "LO<F+XYD}6UO2B7)-B+6J2+Qt9M5+J4T6{"

    print("Original cipher:", cipher_text)
    print("We know the flag starts with 'KPMG'")
    print("First 4 chars of cipher: 'LO<F'")
    print()

    # Let's see what transformations give us KPMG from LO<F
    print("Testing different approaches to get KPMG from LO<F:")

    # Try keyboard shifts
    print("\nKeyboard shifts:")
    for shift in range(-10, 11):
        if shift != 0:
            shifted = keyboard_shift(cipher_text, shift)
            if shifted.startswith('KPMG') or shifted[:4] == 'KPMG':
                print(f"*** FOUND! Shift {shift:+d}: {shifted}")
            elif 'KPMG' in shifted[:10]:  # Check if KPMG appears near the start
                print(f"Shift {shift:+d}: {shifted[:20]}...")

    print("\nTrying layout conversions:")
    dvorak_result = dvorak_to_qwerty(cipher_text)
    if dvorak_result.startswith('KPMG'):
        print(f"*** FOUND! Dvorak to QWERTY: {dvorak_result}")

    qwerty_result = qwerty_to_dvorak(cipher_text)
    if qwerty_result.startswith('KPMG'):
        print(f"*** FOUND! QWERTY to Dvorak: {qwerty_result}")

    # Let's also try manual mapping based on LO<F -> KPMG
    print("\nManual analysis:")
    print("L -> K")
    print("O -> P")
    print("< -> M")
    print("F -> G")

    # Let's look at keyboard positions
    print("\nKeyboard position analysis:")
    print("L is at position in QWERTY row 3")
    print("K is at position in QWERTY row 3")
    print("O is at position in QWERTY row 2")
    print("P is at position in QWERTY row 2")

    # Try left shift by 1 position
    def shift_left_one(text):
        # QWERTY layout mapping - shift each key one position to the left
        mapping = {
            # Row 1
            '1': '`', '2': '1', '3': '2', '4': '3', '5': '4', '6': '5', '7': '6', '8': '7', '9': '8', '0': '9', '-': '0', '=': '-',
            # Row 2
            'q': '`', 'w': 'q', 'e': 'w', 'r': 'e', 't': 'r', 'y': 't', 'u': 'y', 'i': 'u', 'o': 'i', 'p': 'o', '[': 'p', ']': '[', '\\': ']',
            # Row 3
            'a': '`', 's': 'a', 'd': 's', 'f': 'd', 'g': 'f', 'h': 'g', 'j': 'h', 'k': 'j', 'l': 'k', ';': 'l', "'": ';',
            # Row 4
            'z': '`', 'x': 'z', 'c': 'x', 'v': 'c', 'b': 'v', 'n': 'b', 'm': 'n', ',': 'm', '.': ',', '/': '.',
            # Uppercase
            'Q': '~', 'W': 'Q', 'E': 'W', 'R': 'E', 'T': 'R', 'Y': 'T', 'U': 'Y', 'I': 'U', 'O': 'I', 'P': 'O', '{': 'P', '}': '{', '|': '}',
            'A': '~', 'S': 'A', 'D': 'S', 'F': 'D', 'G': 'F', 'H': 'G', 'J': 'H', 'K': 'J', 'L': 'K', ':': 'L', '"': ':',
            'Z': '~', 'X': 'Z', 'C': 'X', 'V': 'C', 'B': 'V', 'N': 'B', 'M': 'N', '<': 'M', '>': '<', '?': '>',
            # Symbols
            '!': '~', '@': '!', '#': '@', '$': '#', '%': '$', '^': '%', '&': '^', '*': '&', '(': '*', ')': '(', '_': ')', '+': '_'
        }

        result = ""
        for char in text:
            result += mapping.get(char, char)
        return result

    left_shift_result = shift_left_one(cipher_text)
    print(f"\nLeft shift by 1: {left_shift_result}")
    if left_shift_result.startswith('KPMG'):
        print("*** FOUND THE SOLUTION! ***")
